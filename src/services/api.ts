export interface RawModelData {
  id: string;
  name: string;
  thumbnailPath: string | null;
  fileType: string | null;
  size: string | null;
  createdAt: string;
  filePath: string; // This is the pathname in blob storage
  url: string;      // This is the full URL to the blob
}

export interface ModelData {
  id: string;
  name: string;
  thumbnailPath: string | null;
  fileType: string | null;
  size: number | null;
  createdAt: string;
  filePath: string; // This will be the full URL
}

// Raw material data from the API, with properties nested in `data`
export interface RawMaterialData {
  id: string;
  model_id: number;
  name: string;
  thumbnailPath: string | null;
  data: {
    color: string;
    metalness: number;
    roughness: number;
    glass: number;
  };
  createdAt: string;
}

// Flattened material data structure for use in frontend components
export interface MaterialData {
  id: string;
  model_id: number;
  name: string;
  thumbnailPath: string | null;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  createdAt: string;
}

// Payload for creating a new material
export interface CreateMaterialPayload {
  model_id: string;
  name: string;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  thumbnailUrl?: string;
}

// Payload for updating an existing material
export interface UpdateMaterialPayload {
  name: string;
  color: string;
  metalness: number;
  roughness: number;
  glass: number;
  thumbnailUrl?: string;
}

// Payload for creating a new model
export interface CreateModelPayload {
  name: string;
  filePath: string;
  thumbnailPath: string;
}

// Payload for updating an existing model
export interface UpdateModelPayload {
  name?: string;
  filePath?: string;
  thumbnailPath?: string | null;
}

// Use relative path for production, absolute for development
const BASE_URL = '/api';

// Unified error handling for API requests
const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP error! status: ${response.status}` }));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }
  // For 204 No Content, response.json() will fail, so we return a placeholder.
  if (response.status === 204) {
    return {} as T;
  }
  return response.json();
};

// Transform model data from API to frontend format
const transformModelData = (model: RawModelData): ModelData => {
  const sizeInBytes = model.size ? parseInt(model.size, 10) : null;
  return {
    id: model.id,
    name: model.name,
    thumbnailPath: model.thumbnailPath,
    fileType: model.fileType,
    size: sizeInBytes && !isNaN(sizeInBytes) ? sizeInBytes : null,
    createdAt: model.createdAt,
    filePath: model.url, // Use the full URL from blob
  };
};

// Transforms raw material data from API into a flattened structure for the frontend
const transformApiMaterialToFrontend = (material: RawMaterialData): MaterialData => {
  return {
    id: material.id,
    model_id: material.model_id,
    name: material.name,
    thumbnailPath: material.thumbnailPath,
    createdAt: material.createdAt,
    ...material.data, // Flatten the nested data object
  };
};

class ApiService {
  // --- Uploader ---
  async uploadFile(
    file: File,
    onUploadProgress?: (progress: number) => void,
    pathname?: string
  ): Promise<{ url: string; pathname: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (pathname) {
        formData.append('pathname', pathname);
      }

      // 使用XMLHttpRequest来支持上传进度
      if (onUploadProgress) {
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open('POST', `${BASE_URL}/upload`, true);
          
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const progress = Math.round((event.loaded / event.total) * 100);
              onUploadProgress(progress);
            }
          };
          
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              const response = JSON.parse(xhr.responseText);
              resolve({
                url: response.filePath,
                pathname: response.filePath
              });
            } else {
              reject(new Error('上传失败'));
            }
          };
          
          xhr.onerror = () => reject(new Error('上传失败'));
          xhr.send(formData);
        });
      } else {
        // 如果不需要进度，使用fetch API
        const response = await fetch(`${BASE_URL}/upload`, {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          throw new Error(`上传失败: ${response.statusText}`);
        }
        
        const data = await response.json();
        return {
          url: data.filePath,
          pathname: data.filePath
        };
      }
    } catch (error) {
      console.error('Upload failed:', error);
      throw new Error('文件上传失败，请检查网络连接或联系管理员。');
    }
  }

  // --- Model Methods ---
  async getModels(): Promise<ModelData[]> {
    try {
      const response = await fetch(`${BASE_URL}/models`);
      const models = await handleApiResponse<RawModelData[]>(response);
      return models.map(transformModelData);
    } catch (error) {
      console.error('Failed to fetch models:', error);
      return [];
    }
  }

  async getModel(id: string): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`);
      const model = await handleApiResponse<RawModelData>(response);
      return transformModelData(model);
    } catch (error) {
      console.error(`Failed to get model ${id}:`, error);
      return null;
    }
  }

  async addModel(modelData: CreateModelPayload): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelData),
      });
      const newModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(newModel);
    } catch (error) {
      console.error('Failed to add model:', error);
      return null;
    }
  }
  
  async updateModel(id: string, data: UpdateModelPayload): Promise<ModelData | null> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const updatedModel = await handleApiResponse<RawModelData>(response);
      return transformModelData(updatedModel);
    } catch (error) {
      console.error(`Failed to update model ${id}:`, error);
      return null;
    }
  }

  async deleteModel(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/models/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete model ${id}:`, error);
      return false;
    }
  }

  // --- Material Methods ---
  async getMaterials(): Promise<MaterialData[]> {
    try {
      const response = await fetch(`${BASE_URL}/materials`);
      const rawMaterials = await handleApiResponse<RawMaterialData[]>(response);
      return rawMaterials.map(transformApiMaterialToFrontend);
    } catch (error) {
      console.error('Failed to fetch materials:', error);
      return [];
    }
  }

  async createMaterial(data: CreateMaterialPayload): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const newRawMaterial = await handleApiResponse<RawMaterialData>(response);
      return transformApiMaterialToFrontend(newRawMaterial);
    } catch (error) {
      console.error('Failed to create material:', error);
      throw error;
    }
  }

  async updateMaterial(id: string, data: UpdateMaterialPayload): Promise<MaterialData> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      const updatedRawMaterial = await handleApiResponse<RawMaterialData>(response);
      return transformApiMaterialToFrontend(updatedRawMaterial);
    } catch (error) {
      console.error(`Failed to update material ${id}:`, error);
      throw error;
    }
  }

  async deleteMaterial(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${BASE_URL}/materials/${id}`, { method: 'DELETE' });
      await handleApiResponse(response);
      return true;
    } catch (error) {
      console.error(`Failed to delete material ${id}:`, error);
      return false;
    }
  }
}

// Export a single instance of the service
export const apiService = new ApiService();