import React, { useState, useEffect } from 'react';
import { ModelCard } from '../components/model-card/model-card';
import { Loading } from '../components/loading/loading';
import { UploadModelCard } from '../components/upload-model-card/upload-model-card';
import { EmptyState } from '../components/empty-state/empty-state';
import { useLogoClick } from '../hooks/useLogoClick';
import { apiService } from '../services/api';
import type { ModelData } from '../services/api';
import './WelcomePage.css';
import LogoSvg from '../assets/images/Logo.svg';

interface WelcomePageProps {
  theme?: 'light' | 'dark';
}

const WelcomePage: React.FC<WelcomePageProps> = ({ theme = 'dark' }) => {
  const [models, setModels] = useState<ModelData[]>([]);
  const [loading, setLoading] = useState(true);
  const { handleLogoClick } = useLogoClick('/admin');

  // 获取模型数据
  useEffect(() => {
    const fetchModels = async () => {
      setLoading(true);
      try {
        const modelData = await apiService.getModels();
        console.log('Fetched models data:', modelData); // 调试：打印获取到的模型数据
        setModels(modelData);
      } catch (error) {
        console.error('获取模型数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchModels();
  }, []);



  return (
    <div className={`welcome-page theme-${theme}`}>
      {/* Logo 直接放在页面左上角 */}
      <img
        className="welcome-logo"
        src={LogoSvg}
        alt="会通智能色彩云库"
        onClick={handleLogoClick}
      />

      {/* 主要内容区域 */}
      <main className="welcome-main">
        <div className="welcome-content">

          {/* 欢迎文字 */}
          <div className="welcome-text">
            <h1 className="welcome-greeting">您好！欢迎使用</h1>
            <h2 className="welcome-title">会通智能色彩云库</h2>
            <p className="welcome-subtitle">请选择一个模型进入渲染</p>
          </div>

          {/* 模型网格 */}
          <div className="welcome-models">
            {loading ? (
              <div style={{ position: 'relative', width: '100%', height: '200px' }}>
                <Loading
                  text="正在加载模型..."
                  centered={true}
                />
              </div>
            ) : models.length > 0 ? (
              <div className="models-grid">
                {models.map((model) => (
                  <ModelCard key={model.id} model={model} />
                ))}
                <UploadModelCard />
              </div>
            ) : (
              <div className="models-grid">
                <UploadModelCard />
                <EmptyState
                  title="暂无可用模型"
                  description="请联系管理员添加模型到云库中"
                  size="medium"
                />
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default WelcomePage;