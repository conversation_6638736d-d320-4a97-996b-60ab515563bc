import dotenv from 'dotenv';
// 优先加载 .env.local，如果不存在则加载 .env
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// --- 基本设置 ---
const app = express();
const prisma = new PrismaClient();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- 中间件 ---
app.use(cors());
app.use(express.json());
// 提供 /uploads 目录下的静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// --- Multer 文件上传设置 ---
// 确保 uploads 目录存在
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // 创建一个唯一的文件名，以避免重名冲突
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// --- 工具函数 ---
// 删除本地文件的辅助函数
function deleteFile(filePath) {
  if (!filePath) return;
  
  // 从完整URL中提取文件路径
  let localPath;
  if (filePath.startsWith('/uploads/')) {
    localPath = path.join(__dirname, filePath);
  } else if (filePath.startsWith('http')) {
    // 如果是完整URL，提取路径部分
    const url = new URL(filePath);
    if (url.pathname.startsWith('/uploads/')) {
      localPath = path.join(__dirname, url.pathname);
    }
  }
  
  if (localPath && fs.existsSync(localPath)) {
    try {
      fs.unlinkSync(localPath);
      console.log(`✅ Deleted file: ${localPath}`);
    } catch (error) {
      console.error(`❌ Failed to delete file ${localPath}:`, error);
    }
  }
}

// --- API 路由 ---

// 新的文件上传端点
app.post('/api/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded.' });
  }
  // 返回文件的访问路径
  res.status(200).json({ filePath: `/uploads/${req.file.filename}` });
});

// GET all models
app.get('/api/models', async (req, res) => {
  try {
    const modelsFromDb = await prisma.model.findMany({
      orderBy: { createdAt: 'desc' },
    });
    res.json(modelsFromDb);
  } catch (error) {
    console.error('Failed to fetch models:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET a single model by ID
app.get('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
      return res.status(400).json({ error: 'Invalid model ID.' });
    }

    const model = await prisma.model.findUnique({ where: { id: modelId } });
    if (!model) {
      return res.status(404).json({ error: 'Model not found' });
    }

    res.json(model);
  } catch (error) {
    console.error(`Failed to fetch model ${req.params.id}:`, error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST a new model
app.post('/api/models', async (req, res) => {
  const { name, filePath, thumbnailPath } = req.body;
  if (!name) {
    return res.status(400).json({ error: 'Name is required.' });
  }

  try {
    const newModel = await prisma.model.create({
      data: {
        name,
        filePath: filePath || '',
        thumbnailPath: thumbnailPath || '',
      },
    });
    res.status(201).json(newModel);
  } catch (error) {
    console.error('Failed to create model record:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// PUT to update a model
app.put('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
      return res.status(400).json({ error: 'Invalid model ID.' });
    }

    const { name, filePath, thumbnailPath } = req.body;
    const updatedModel = await prisma.model.update({
      where: { id: modelId },
      data: {
        name,
        filePath,
        thumbnailPath,
      },
    });

    res.json(updatedModel);
  } catch (error) {
    console.error(`Failed to update model ${req.params.id}:`, error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Model not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE a model
app.delete('/api/models/:id', async (req, res) => {
  try {
    const modelId = parseInt(req.params.id, 10);
    if (isNaN(modelId)) {
      return res.status(400).json({ error: 'Invalid model ID.' });
    }

    const model = await prisma.model.findUnique({ where: { id: modelId } });
    if (!model) {
      return res.status(404).json({ error: 'Model not found' });
    }

    // Delete associated files from local storage
    deleteFile(model.filePath);
    deleteFile(model.thumbnailPath);

    // Delete from database
    await prisma.model.delete({ where: { id: modelId } });

    res.status(204).send();
  } catch (error) {
    console.error(`Failed to delete model ${req.params.id}:`, error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Model not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET all materials
app.get('/api/materials', async (req, res) => {
  try {
    const materials = await prisma.material.findMany({
      orderBy: { createdAt: 'desc' },
    });
    res.json(materials);
  } catch (error) {
    console.error('Failed to fetch materials:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST a new material
app.post('/api/materials', async (req, res) => {
  const { model_id, name, color, metalness, roughness, glass, thumbnailPath } = req.body;

  if (!model_id || !name) {
    return res.status(400).json({ error: '`model_id` and `name` are required fields.' });
  }

  try {
    const newMaterial = await prisma.material.create({
      data: {
        model_id: parseInt(model_id, 10),
        name,
        data: {
          color: color || '#ffffff',
          metalness: metalness !== undefined ? metalness : 0,
          roughness: roughness !== undefined ? roughness : 0.5,
          glass: glass !== undefined ? glass : false,
        },
        thumbnailPath: thumbnailPath || '',
      },
    });
    res.status(201).json(newMaterial);
  } catch (error) {
    console.error('Failed to create material record:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// DELETE a material
app.delete('/api/materials/:id', async (req, res) => {
  try {
    const materialId = parseInt(req.params.id, 10);
    if (isNaN(materialId)) {
      return res.status(400).json({ error: 'Invalid material ID.' });
    }

    const material = await prisma.material.findUnique({ where: { id: materialId } });
    if (!material) {
      return res.status(404).json({ error: 'Material not found' });
    }

    // Delete associated thumbnail from local storage
    deleteFile(material.thumbnailPath);

    // Delete from database
    await prisma.material.delete({ where: { id: materialId } });

    res.status(204).send();
  } catch (error) {
    console.error(`Failed to delete material ${req.params.id}:`, error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Material not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 导出 app 以供其他模块使用（如测试）
export default app;

// 只有在非生产环境或非 Vercel 环境下才启动服务器
if (process.env.NODE_ENV !== 'production' || !process.env.VERCEL) {
  const PORT = process.env.PORT || 3001;

  const server = app.listen(PORT, () => {
    console.log(`🚀 Backend server is running on http://localhost:${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🗄️  Database: ${process.env.POSTGRES_PRISMA_URL ? 'Connected' : 'Not configured'}`);
  });

  process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
      console.log('✅ Process terminated');
      process.exit(0);
    });
  });

  process.on('SIGINT', () => {
    console.log('🛑 SIGINT received, shutting down gracefully');
    server.close(() => {
      console.log('✅ Process terminated');
      process.exit(0);
    });
  });

  server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.error(`❌ Port ${PORT} is already in use.`);
      process.exit(1);
    } else {
      console.error('❌ Server error:', err);
      process.exit(1);
    }
  });
}
