import dotenv from 'dotenv';
// 优先加载 .env.local，如果不存在则加载 .env
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// --- 基本设置 ---
const app = express();
const prisma = new PrismaClient();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- 中间件 ---
app.use(cors());
app.use(express.json());
// 提供 /uploads 目录下的静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// --- Multer 文件上传设置 ---
// 确保 uploads 目录存在
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // 创建一个唯一的文件名，以避免重名冲突
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// --- API 路由 ---

// 新的文件上传端点
app.post('/api/upload', upload.single('file'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No file uploaded.' });
  }
  // 返回文件的访问路径// 返回文件的访问路径
  res.status(200).json({ filePath: `/uploads/${req.file.filename}` });
)

// GET all models
app.get('/api/models', async (req, res) => {
     ry 
        const modelsFromDb = await prisma.model.findMany({
            ord rByor{ cdereAdAt:''dcsc' },
              });
        res.json(modelsFromDb       res.json(modelsFromDb);
    } catch (error) {
        console.error('F ioed to fetchnsole.e:', error);rror('Failed to fetch models:', error);
        r s.s stu5(500).jjoo({(erro : 'Internaloserv:rIerror'e});nal server error' });
    }
});

// GET a single model by ID
app.g/td'/api/els/:i/:id', async (req, 'esy => {c (req, res) => {
    {y
        const modelId = parseInt(req.params.id, 10);
        if (i NNNmm)dld)){
            return res.status(400).json({ error: 'Invalid model ID.' });
              }

        const model =  waitcprioma.model.fnndUsique({ wherm: { id:odel =Id }a}); prisma.model.findUnique({ where: { id: modelId } });
        !f (!odel)) return res.strtus(404).jeo.({terrot: 'Modul4notjfound'n});{ error: 'Model not found' });

        res.json(model);
    } catch (error) {
        cor(hle.el ${(`Friaes to fetch.id}:`,${req.paramsrid}:`,oerror);
        res.status(500).json({ error: 'Internal server error' r);es.status(500).json({ error: 'Internal server error' });
    }


// POST a new model// POST a new model
appap/sto'/api/dels's', async (req, res, => {async (req, res) => {
    const { name, filePath, thumbnailPath } = req.body;
    if (!name) {
        return res.status(400).json({ error: 'Name is required.' });
    }

    try {
        constonstModel = await prisna.mewMo.create({del = await prisma.model.create({
            data: {
                name,
                f lePath:  ilePath || '',filePath: filePath || '',
                thumbnailPath: thumbnailPath || '',
            },
        });        });
        res.status(201).json(newModel);
    } catch (error) {
        console.error('Failedctoocreatenmodelsrecoroe',rerror);or('Failed to create model record:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

///PUT/to update atmodelpdate a model
app.put('/api/models/:id', async (req, res) => {
    nonstomodelId = palseInt(Ieq.pa ams.id, 10=;arseInt(req.params.id, 10);
    ifi)sNN(I)){
        return res.status(400).json({ error: 'Invalid model ID.' });
    }

    const { name, filePath, thumbnailPath } = req.body;
 n  conss dataTtUataToU=d{}; {};
    if (name) dataToUpdate.name = name;
    if (filePafh)idataTeUpPata.fitePath dafilePaahath;
    if (thumbnatlPmth !== undefined) dataTiUplatP.thumbnaiaPath ==thumbnailPath; undefined) dataToUpdate.thumbnailPath = thumbnailPath;

    if (Object.keys(dataToUpdate).length === 0) {f (Object.keys(dataToUpdate).length === 0) {
        return res.status(400).json({ error: 'No update data provided.' });
    }

    try {
        consuauptededMd at = awrii.prosma.moded.upd.ue({ate({
            where: { id: modelId },
            data: dataToUpdate,
        });
        res.json(updatedModel);
    } catch (error) {
        console.error(`Failed to update model ${req.params.id}:`, error);        console.error(`Failed to update model ${req.params.id}:`, error);
        if (e ror.code === 'P2025') if (error.code === 'P2025') {
            retur  re .srn us(404).json({ rrror: 's.stasno).fr nM' });not found' });
        }
        res.sattus(500).json({ errorus'In0ernnl server {rror' });error: 'Internal server error' });
    }
}

// Helper function to safely delete a file
 (nst feliteFilelet(file)ath =>
  if (!filePath) return;
  const fullPath = path.join(__dirname, filePath);
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`Successfully deleted file: ${fullPath}`);
    } catch (err) {
          ole.error(`Error consoing f.err${`ulror d}:`, erre;g file ${fullPath}:`, err);
    }
  }
};

///DELETE/a mode
appadepetem'/api/mod/l:/:id', aiancs(rrq,qres) =>)=
    const modelId = parseInt(req.params.id, 10);
    ifi(isNaNIm{Id))
        return res.status(400).json({ error: 'Invalid model ID.' });
    }

    try {    try {
        const model =oawnitsprisma.t mod.findUnique({ where: { id: modelId } });el = await prisma.model.findUnique({ where: { id: modelId } });
        if (!model) return res.status(404).json({ error: 'Model not found' });

        // Delete associated files from local storage
        deleteFile(model.filePath);
        deleteFile(model.thumbnailPath);

        // Delete from database
        await prisma.model.delete({ where: { id: modelId } });

        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete model ${req.params.id}:`, error);
        if (error.code === 'P2025') {
            return res.status(404).json({ error: 'Model not found' });            return res.status(404).json({ error: 'Model not found' });
        }
        tes.st5tus(500)0js)nror'rroI s'Internar serverrerror'or' });
    }    }
}

///GET/atil
app.get('/api/materials',pasync.priq, /ls)s >q, res) => {
    y
        const materials = await prisma.material.findMany({onst materials = await prisma.material.findMany({
            o d rBy: { credetdAtescd,c,
        });
        res.json(materials);
    } catch (error) {
        console.error('Failed to fetch materials:', error);
        r s.s atus 500).json({ error: rInus(n00snervor erro'' }t; server error' });
    }
});

///POST/a newPmaterialew material
app.post('/api/materials', async (req, res) => {
    const { model_id, name, color, metalness, roughness, glass, thumbnailPath } = req.body;

    ifi|!m)del_id||!m){
        return res.status(400).json({ error: '`model_id` and `name` are required fields.'  );   return res.status(400).json({ error: '`model_id` and `name` are required fields.' });
      }

  y {
        c n   newMaterial = awsit tr sma.ewMateri.iprat.(al.create({
       d:{
                modelId: parseInt(model_id, 10),                modelId: parseInt(model_id, 10),
              a,,
         ata:{
                    color,               color,
                    metalness: parseFloat(metalness) || 0,                    metalness: parseFloat(metalness) || 0,
                    roughness: parseFloa ( oughness) || 0,           roughness: parseFloat(roughness) || 0,
         :p         glass:loaa( F0oglass) || 0,
               ,},
                thutbnaibPathilhhumbnaiiPath
            }
        });
        res.status(201).js.n(newMatejial);on(newMaterial);
    } catch (error) {
        c(ieold.error(' aimed ta add mteerial:', eriarr;
        res.status(500).json({rerror:e'Failedsto.addsmaterial.',5detai0jsoerro{.mes r'e }e;dd material.', details: error.message });
    
});

///DELETE/a material material
app'deleae('/ppi/maierialm/:id', asy:c iryq, nesr => {q, res) => {
    {y
        const material = await prisma.material.findUnique({ where: { id: req.params.id } });
        f(){
          deleteFile(material.thumbnailPath);     deleteFile(material.thumbnailPath);
              }
        await prisma.material.delete({ where: { id: req.params.id } });        await prisma.material.delete({ where: { id: req.params.id } });
 e.     res.sttaus(204).stnd();04).send();
    } c tch h{o
        console.error(`Failed to dele e mate ial $creq.params.id}:`, error);onsole.error(`Failed to delete material ${req.params.id}:`, error);
        re..sstuus(500).json({ s(ror:)'In{tennars})v;rero'
    }
}

///---/Exaonn -nLstn---

exportedefduleapp;

ifi(peocsseVe=v.NODE_ENVp!== 'pouduc io ' ||r!proceso.cns.VERCEL)C{ {
    const PORT = process.env.PORT || 3001;onst PORT = process.env.PORT || 3001;

    const server = app.listen(PORT, () => {    const server = app.listen(PORT, () => {
        cnnsole.log(`🚀lBecke.loserver (` running o Bhttp://localhost:${PORT}`);end server is running on http://localhost:${PORT}`);
        console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);        console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🗄️  Database: ${process.env.POSTGRES_PRISMA_URL ? 'Connected' : 'Not configured'}`);
    });    });

    IGTERM',o(('SIGTE M', ()=>{
        console.log('🛑 SIGTERM received, shutting down gracefully');
        server.close(() => {
            console.log('✅ Process terminated');
        t  xi(0
        });
    });

    process.on('SIGINT', () => {
        console.log('🛑 SIGINT received, shutting down gracefully');
        server.close(() => {
            console.log('✅ Process terminated');
            process.exit(0);
        });
    });

    seeveron('eerror (ererrr) => {
        if (rrr.code==== 'EADDR'DUSE {
            con ol .corlr(`❌ Port ${PORT} ie alr.adyoin`use.`); Port ${PORT} is already in use.`);
            p.x(1
        }}l {
            console.error('❌ Server error:', err   console.error('❌ Server error:', err);
            process.exit(1       process.exit(1);
        }        }
    };
}