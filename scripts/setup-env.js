#!/usr/bin/env node

/**
 * 跨平台环境设置脚本
 * 自动检测和配置开发环境
 */

import { writeFileSync, existsSync, readFileSync } from 'fs';
import { platform } from 'os';
import { execSync } from 'child_process';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 环境变量模板
const ENV_TEMPLATE = `# Huitong Material Environment Configuration
# Generated by setup-env.js on ${new Date().toISOString()}

# Database Configuration
POSTGRES_PRISMA_URL="your_postgres_url_here"
POSTGRES_URL_NON_POOLING="your_postgres_direct_url_here"

# Development Configuration
NODE_ENV=development
PORT=3001

# Production Configuration (for Aliyun deployment)
# ALIYUN_ACCESS_KEY_ID="your_aliyun_access_key_here"
# ALIYUN_ACCESS_KEY_SECRET="your_aliyun_secret_here"

# Security Note:
# Never commit this file to version control if it contains real credentials
# Add .env.local to your .gitignore file
`;

/**
 * 检查系统要求
 */
function checkSystemRequirements() {
  log('🔍 Checking system requirements...', 'cyan');
  
  const requirements = [
    {
      name: 'Node.js',
      command: 'node --version',
      minVersion: '18.0.0',
      check: (version) => {
        const versionNumber = version.replace('v', '');
        const [major] = versionNumber.split('.').map(Number);
        return major >= 18;
      }
    },
    {
      name: 'npm',
      command: 'npm --version',
      minVersion: '8.0.0',
      check: (version) => {
        const [major] = version.split('.').map(Number);
        return major >= 8;
      }
    }
  ];
  
  let allPassed = true;
  
  for (const req of requirements) {
    try {
      const version = execSync(req.command, { encoding: 'utf8' }).trim();
      const passed = req.check(version);
      
      log(`   ${passed ? '✅' : '❌'} ${req.name}: ${version} (min: ${req.minVersion})`, 
          passed ? 'green' : 'red');
      
      if (!passed) allPassed = false;
    } catch {
      log(`   ❌ ${req.name}: Not installed`, 'red');
      allPassed = false;
    }
  }
  
  return allPassed;
}

/**
 * 创建环境配置文件
 */
function createEnvFile() {
  log('\n📝 Setting up environment configuration...', 'cyan');
  
  const envPath = '.env.local';
  
  if (existsSync(envPath)) {
    log(`   ⚠️  ${envPath} already exists`, 'yellow');
    
    // 读取现有文件并检查是否需要更新
    const existingContent = readFileSync(envPath, 'utf8');
    
    if (existingContent.includes('your_postgres_url_here')) {
      log('   💡 Environment file contains placeholder values', 'yellow');
      log('   📝 Please update the values in .env.local with your actual configuration', 'yellow');
    } else {
      log('   ✅ Environment file appears to be configured', 'green');
    }
    
    return true;
  }
  
  try {
    writeFileSync(envPath, ENV_TEMPLATE);
    log(`   ✅ Created ${envPath}`, 'green');
    log('   📝 Please update the placeholder values with your actual configuration', 'yellow');
    return true;
  } catch (error) {
    log(`   ❌ Failed to create ${envPath}: ${error.message}`, 'red');
    return false;
  }
}

/**
 * 安装依赖项
 */
function installDependencies() {
  log('\n📦 Installing dependencies...', 'cyan');
  
  if (!existsSync('node_modules')) {
    try {
      log('   🔄 Running npm install...', 'blue');
      execSync('npm install', { stdio: 'inherit' });
      log('   ✅ Dependencies installed successfully', 'green');
      return true;
    } catch (error) {
      log(`   ❌ Failed to install dependencies: ${error.message}`, 'red');
      return false;
    }
  } else {
    log('   ✅ Dependencies already installed', 'green');
    
    // 检查是否需要更新
    try {
      execSync('npm outdated', { stdio: 'pipe' });
      log('   💡 Some packages may be outdated. Run "npm update" to update them.', 'yellow');
    } catch {
      // npm outdated returns non-zero exit code when packages are outdated
      // This is expected behavior
    }
    
    return true;
  }
}

/**
 * 设置Git hooks（如果使用Git）
 */
function setupGitHooks() {
  log('\n🔧 Setting up Git hooks...', 'cyan');
  
  try {
    // 检查是否是Git仓库
    execSync('git rev-parse --git-dir', { stdio: 'pipe' });
    
    // 检查是否已安装husky
    if (existsSync('node_modules/husky')) {
      try {
        execSync('npm run prepare', { stdio: 'pipe' });
        log('   ✅ Git hooks configured with Husky', 'green');
      } catch {
        log('   ⚠️  Failed to setup Husky hooks', 'yellow');
      }
    } else {
      log('   ⚪ Husky not installed, skipping Git hooks setup', 'yellow');
    }
    
    return true;
  } catch {
    log('   ⚪ Not a Git repository, skipping Git hooks setup', 'yellow');
    return true;
  }
}

/**
 * 生成Prisma客户端
 */
function generatePrismaClient() {
  log('\n🗄️  Setting up database client...', 'cyan');
  
  if (existsSync('prisma/schema.prisma')) {
    try {
      execSync('npx prisma generate', { stdio: 'inherit' });
      log('   ✅ Prisma client generated successfully', 'green');
      return true;
    } catch (error) {
      log(`   ❌ Failed to generate Prisma client: ${error.message}`, 'red');
      return false;
    }
  } else {
    log('   ⚪ No Prisma schema found, skipping client generation', 'yellow');
    return true;
  }
}

/**
 * 显示下一步指导
 */
function showNextSteps() {
  log('\n🎯 Next Steps:', 'cyan');
  log('   1. Update .env.local with your actual database and API credentials', 'blue');
  log('   2. Run "npm run check:env" to verify your configuration', 'blue');
  log('   3. Run "npm run dev:safe" to start the development server', 'blue');
  log('   4. Visit http://localhost:5176 to see your application', 'blue');
  
  log('\n📚 Useful Commands:', 'cyan');
  log('   npm run dev:safe     - Start development server with safety checks', 'blue');
  log('   npm run check:env    - Check environment configuration', 'blue');
  log('   npm run clean        - Clean build cache and temporary files', 'blue');
  log('   npm run db:studio    - Open Prisma Studio for database management', 'blue');
}

/**
 * 主设置函数
 */
function main() {
  log('🚀 Huitong Material Environment Setup', 'bright');
  log(`📍 Platform: ${platform()}`, 'blue');
  log('=' .repeat(50), 'blue');
  
  const checks = [
    { name: 'System Requirements', fn: checkSystemRequirements },
    { name: 'Environment Configuration', fn: createEnvFile },
    { name: 'Dependencies', fn: installDependencies },
    { name: 'Git Hooks', fn: setupGitHooks },
    { name: 'Database Client', fn: generatePrismaClient }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    const passed = check.fn();
    if (!passed) allPassed = false;
  }
  
  log('\n' + '='.repeat(50), 'blue');
  
  if (allPassed) {
    log('✅ Environment setup completed successfully!', 'green');
    showNextSteps();
  } else {
    log('❌ Environment setup completed with some issues', 'yellow');
    log('💡 Please resolve the issues above and run the setup again', 'yellow');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// 错误处理
process.on('uncaughtException', (error) => {
  log(`❌ Setup failed: ${error.message}`, 'red');
  process.exit(1);
});

// 运行主函数
main();
