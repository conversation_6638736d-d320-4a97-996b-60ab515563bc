#!/bin/bash

# 部署脚本 - 用于在阿里云服务器上部署应用

# 设置变量
SERVER_IP="**************"
SSH_USER="root"
SSH_PASSWORD="3pkRYYwtFzFD@*V"
PROJECT_DIR="/opt/huitong-material"

# 安装必要的软件
install_dependencies() {
    echo "正在更新系统包..."
    sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no ${SSH_USER}@${SERVER_IP} "
        apt update && apt upgrade -y
        if ! command -v docker &> /dev/null; then
            echo "正在安装 Docker..."
            curl -fsSL https://get.docker.com | sh
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            echo "正在安装 Docker Compose..."
            apt install docker-compose-plugin -y
        fi
        
        echo "正在安装其他必要软件..."
        apt install -y git sshpass
    "
}

# 配置防火墙
setup_firewall() {
    echo "正在配置防火墙..."
    sshpass -p "$SSH_PASSWORD" ssh ${SSH_USER}@${SERVER_IP} "
        ufw allow 22/tcp
        ufw allow 80/tcp
        ufw allow 443/tcp
        echo "y" | ufw enable
    "
}

# 部署应用
deploy_app() {
    echo "正在部署应用到服务器..."
    
    # 创建项目目录
    sshpass -p "$SSH_PASSWORD" ssh ${SSH_USER}@${SERVER_IP} "
        mkdir -p ${PROJECT_DIR}
    "
    
    # 复制本地文件到服务器
    echo "正在上传文件到服务器..."
    sshpass -p "$SSH_PASSWORD" rsync -avz --delete \
        --exclude='.git' \
        --exclude='node_modules' \
        --exclude='.env' \
        --exclude='.env.*' \
        -e ssh \
        ./ ${SSH_USER}@${SERVER_IP}:${PROJECT_DIR}/
    
    # 设置环境变量
    echo "正在设置环境变量..."
    sshpass -p "$SSH_PASSWORD" scp .env.example ${SSH_USER}@${SERVER_IP}:${PROJECT_DIR}/.env
    
    # 构建并启动服务
    echo "正在启动服务..."
    sshpass -p "$SSH_PASSWORD" ssh ${SSH_USER}@${SERVER_IP} "
        cd ${PROJECT_DIR}
        docker compose down
        docker compose pull
        docker compose build --no-cache
        docker compose up -d
        
        # 等待服务启动
        sleep 10
        
        # 运行数据库迁移
        docker compose exec -T app npx prisma migrate deploy
    "
    
    echo "部署完成！"
    echo "应用已成功部署到: http://${SERVER_IP}"
}

# 主函数
main() {
    echo "=== 开始部署 huitong-material 应用到阿里云服务器 ==="
    
    # 安装依赖
    install_dependencies
    
    # 配置防火墙
    setup_firewall
    
    # 部署应用
    deploy_app
    
    echo "=== 部署完成 ==="
}

# 执行主函数
main "$@"
