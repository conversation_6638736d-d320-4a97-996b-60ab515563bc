#!/usr/bin/env node

/**
 * 生产环境启动脚本
 * 用于在生产环境中启动应用，包含健康检查和监控
 */

import { spawn } from 'child_process';
import { platform } from 'os';
import { createRequire } from 'module';
import dotenv from 'dotenv';

const require = createRequire(import.meta.url);
const { execSync } = require('child_process');

// 加载环境变量
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

// 配置
const CONFIG = {
  PORT: process.env.PORT || 3001,
  NODE_ENV: 'production'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查生产环境要求
function checkProductionRequirements() {
  log('🔍 Checking production requirements...', 'cyan');
  
  // 检查必需的环境变量
  const requiredEnvVars = [
    'POSTGRES_PRISMA_URL'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length > 0) {
    log(`❌ Missing environment variables: ${missing.join(', ')}`, 'red');
    process.exit(1);
  }
  
  // 检查构建文件
  try {
    execSync('ls dist', { stdio: 'pipe' });
    log('✅ Build files found', 'green');
  } catch {
    log('⚠️  Build files not found, running build...', 'yellow');
    try {
      execSync('npm run build', { stdio: 'inherit' });
      log('✅ Build completed', 'green');
    } catch (error) {
      log(`❌ Build failed: ${error.message}`, 'red');
      process.exit(1);
    }
  }
  
  log('✅ Production requirements satisfied', 'green');
}

// 启动生产服务器
function startProduction() {
  log('🚀 Starting production server...', 'cyan');
  
  const isWindows = platform() === 'win32';
  const nodeCmd = isWindows ? 'node.exe' : 'node';
  
  const prodProcess = spawn(nodeCmd, ['backend/server.mjs'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: CONFIG.NODE_ENV,
      PORT: CONFIG.PORT
    }
  });
  
  prodProcess.on('error', (error) => {
    log(`❌ Failed to start production server: ${error.message}`, 'red');
    process.exit(1);
  });
  
  prodProcess.on('exit', (code) => {
    if (code !== 0) {
      log(`❌ Production server exited with code ${code}`, 'red');
      process.exit(code);
    }
  });
  
  // 优雅关闭处理
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down production server...', 'yellow');
    prodProcess.kill('SIGINT');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    log('\n🛑 Shutting down production server...', 'yellow');
    prodProcess.kill('SIGTERM');
    process.exit(0);
  });
  
  // 健康检查
  setTimeout(() => {
    try {
      const healthCheck = execSync(`curl -f http://localhost:${CONFIG.PORT}/api/models`, { 
        encoding: 'utf8',
        timeout: 5000 
      });
      log('✅ Health check passed', 'green');
    } catch {
      log('⚠️  Health check failed - server may still be starting', 'yellow');
    }
  }, 3000);
}

// 主函数
async function main() {
  try {
    log('🎯 Starting Huitong Material Production Environment', 'bright');
    log(`📍 Platform: ${platform()}`, 'blue');
    log(`📍 Node.js: ${process.version}`, 'blue');
    log(`📍 Port: ${CONFIG.PORT}`, 'blue');
    
    checkProductionRequirements();
    startProduction();
    
  } catch (error) {
    log(`❌ Production startup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行主函数
main();
