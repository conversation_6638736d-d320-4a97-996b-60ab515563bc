import dotenv from 'dotenv';
import path from 'path';
import { list, del, copy } from '@vercel/blob';
import { PrismaClient } from '@prisma/client';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

const getDbUrl = () => {
  const dbUrl = process.env.POSTGRES_PRISMA_URL;
  if (dbUrl && dbUrl.includes('vercel-storage.com')) {
    try {
      const url = new URL(dbUrl);
      if (!url.searchParams.has('pgbouncer')) {
        url.searchParams.set('pgbouncer', 'true');
      }
      if (!url.searchParams.has('sslmode')) {
        url.searchParams.set('sslmode', 'require');
      }
      return url.toString();
    } catch (e) {
      console.error("Invalid POSTGRES_PRISMA_URL:", e);
      return dbUrl;
    }
  }
  return dbUrl;
};

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: getDbUrl(),
    },
  },
});

async function migrateBlobStructure() {
  console.log('🚀 Starting Blob structure migration...');

  try {
    const models = await prisma.model.findMany();
    console.log(`🔍 Found ${models.length} models to process.`);

    for (const model of models) {
      console.log(`\n--- Processing Model ID: ${model.id} ---`);
      const updates = {};
      const oldUrlsToDelete = [];
      const newUrlsToRollback = [];

      // --- Migrate Model File ---
      const oldModelUrl = model.filePath;
      if (oldModelUrl && !oldModelUrl.includes(`/models/${model.id}/`)) {
        try {
          const filename = path.basename(new URL(oldModelUrl).pathname);
          const newPathname = `models/${model.id}/${filename}`;
          console.log(`  - Migrating model file: ${filename} -> ${newPathname}`);
          const newBlob = await copy(oldModelUrl, newPathname, { access: 'public' });
          updates.filePath = newBlob.url;
          oldUrlsToDelete.push(oldModelUrl);
          newUrlsToRollback.push(newBlob.url);
        } catch (error) {
          console.error(`  ❌ Error migrating model file ${oldModelUrl}:`, error.message);
          continue; // Skip to next model if a file is missing from blob storage
        }
      }

      // --- Migrate Thumbnail File ---
      const oldThumbnailUrl = model.thumbnailPath;
      if (oldThumbnailUrl && !oldThumbnailUrl.includes(`/models/${model.id}/`)) {
        try {
          const filename = path.basename(new URL(oldThumbnailUrl).pathname);
          const newPathname = `models/${model.id}/${filename}`;
          console.log(`  - Migrating thumbnail: ${filename} -> ${newPathname}`);
          const newBlob = await copy(oldThumbnailUrl, newPathname, { access: 'public' });
          updates.thumbnailPath = newBlob.url;
          oldUrlsToDelete.push(oldThumbnailUrl);
          newUrlsToRollback.push(newBlob.url);
        } catch (error) {
          console.error(`  ❌ Error migrating thumbnail file ${oldThumbnailUrl}:`, error.message);
          // Don't skip here, model file might have been migrated
        }
      }

      // --- Update Database and Cleanup ---
      if (Object.keys(updates).length > 0) {
        try {
          console.log(`  - Updating database for model ${model.id}...`);
          await prisma.model.update({
            where: { id: model.id },
            data: updates,
          });
          console.log(`  ✅ Database updated for model ${model.id}.`);

          if (oldUrlsToDelete.length > 0) {
            console.log(`  - Deleting old blobs for model ${model.id}...`);
            await del(oldUrlsToDelete);
            console.log(`  ✅ Old blobs deleted.`);
          }
        } catch (dbError) {
          console.error(`  ❌ Error updating database for model ${model.id}:`, dbError);
          if (newUrlsToRollback.length > 0) {
            console.log(`  - Rolling back by deleting newly created blobs...`);
            await del(newUrlsToRollback);
            console.log(`  ✅ Rollback complete.`);
          }
        }
      } else {
        console.log(`  - Model ${model.id} is already migrated. Skipping.`);
      }
    }

    console.log('\n🎉 Migration process complete.');

  } catch (error) {
    console.error('❌ An unexpected error occurred during the migration process:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed.');
  }
}

migrateBlobStructure();
